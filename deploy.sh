#!/bin/bash

# Script de Deploy para SD-Resolv Website
# Autor: SD-Resolv Team
# Versão: 1.0

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Variáveis
IMAGE_NAME="sd-resolv-website"
CONTAINER_NAME="sd-resolv-web"
PORT="8080"

# Função para imprimir mensagens coloridas
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  SD-Resolv Website Deploy${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Função para verificar se o Docker está instalado
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker não está instalado. Por favor, instale o Docker primeiro."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_warning "Docker Compose não encontrado. Usando docker compose..."
    fi
}

# Função para parar e remover container existente
cleanup() {
    print_message "Limpando containers existentes..."
    
    if docker ps -q -f name=$CONTAINER_NAME | grep -q .; then
        print_message "Parando container existente..."
        docker stop $CONTAINER_NAME
    fi
    
    if docker ps -aq -f name=$CONTAINER_NAME | grep -q .; then
        print_message "Removendo container existente..."
        docker rm $CONTAINER_NAME
    fi
}

# Função para build da imagem
build_image() {
    print_message "Construindo a imagem Docker..."
    docker build -t $IMAGE_NAME:latest .
    
    if [ $? -eq 0 ]; then
        print_message "Imagem construída com sucesso!"
    else
        print_error "Falha ao construir a imagem!"
        exit 1
    fi
}

# Função para executar o container
run_container() {
    print_message "Iniciando o container..."
    
    docker run -d \
        --name $CONTAINER_NAME \
        -p $PORT:80 \
        --restart unless-stopped \
        $IMAGE_NAME:latest
    
    if [ $? -eq 0 ]; then
        print_message "Container iniciado com sucesso!"
        print_message "Website disponível em: http://localhost:$PORT"
    else
        print_error "Falha ao iniciar o container!"
        exit 1
    fi
}

# Função para usar docker-compose
deploy_with_compose() {
    print_message "Usando Docker Compose para deploy..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose down
        docker-compose up -d --build
    else
        docker compose down
        docker compose up -d --build
    fi
    
    if [ $? -eq 0 ]; then
        print_message "Deploy realizado com sucesso via Docker Compose!"
        print_message "Website disponível em: http://localhost:$PORT"
    else
        print_error "Falha no deploy via Docker Compose!"
        exit 1
    fi
}

# Função para verificar saúde do container
health_check() {
    print_message "Verificando saúde do container..."
    sleep 5
    
    if curl -f http://localhost:$PORT > /dev/null 2>&1; then
        print_message "✅ Website está respondendo corretamente!"
    else
        print_warning "⚠️  Website pode não estar respondendo ainda. Aguarde alguns segundos."
    fi
}

# Função para mostrar logs
show_logs() {
    print_message "Últimos logs do container:"
    docker logs --tail 20 $CONTAINER_NAME
}

# Função principal
main() {
    print_header
    
    # Verificar argumentos
    case "${1:-deploy}" in
        "build")
            check_docker
            build_image
            ;;
        "deploy")
            check_docker
            cleanup
            build_image
            run_container
            health_check
            ;;
        "compose")
            check_docker
            deploy_with_compose
            health_check
            ;;
        "logs")
            show_logs
            ;;
        "stop")
            print_message "Parando o container..."
            docker stop $CONTAINER_NAME
            ;;
        "restart")
            print_message "Reiniciando o container..."
            docker restart $CONTAINER_NAME
            health_check
            ;;
        "clean")
            cleanup
            print_message "Limpeza concluída!"
            ;;
        *)
            echo "Uso: $0 {build|deploy|compose|logs|stop|restart|clean}"
            echo ""
            echo "Comandos disponíveis:"
            echo "  build    - Apenas constrói a imagem Docker"
            echo "  deploy   - Deploy completo (padrão)"
            echo "  compose  - Deploy usando Docker Compose"
            echo "  logs     - Mostra os logs do container"
            echo "  stop     - Para o container"
            echo "  restart  - Reinicia o container"
            echo "  clean    - Remove containers existentes"
            exit 1
            ;;
    esac
}

# Executar função principal
main "$@"
