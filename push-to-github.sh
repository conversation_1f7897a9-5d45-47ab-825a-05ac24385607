#!/bin/bash

# Script para fazer push do SD-Resolv para o GitHub
# Uso: ./push-to-github.sh SEU_TOKEN

set -e

# Cores para output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${GREEN}🚀 SD-Resolv GitHub Push Script${NC}"
echo "=================================="

# Verificar se o token foi fornecido
if [ -z "$1" ]; then
    echo -e "${RED}❌ Erro: Token não fornecido${NC}"
    echo ""
    echo "Como usar:"
    echo "  ./push-to-github.sh SEU_TOKEN"
    echo ""
    echo "Para obter um token:"
    echo "  1. Acesse: https://github.com/settings/tokens"
    echo "  2. Clique em 'Generate new token (classic)'"
    echo "  3. Marque apenas 'repo'"
    echo "  4. Copie o token gerado"
    echo ""
    exit 1
fi

TOKEN=$1
REPO_URL="https://${TOKEN}@github.com/Homura00/sdresolve.git"

echo -e "${YELLOW}📋 Verificando status do repositório...${NC}"

# Verificar se estamos no diretório correto
if [ ! -d ".git" ]; then
    echo -e "${RED}❌ Erro: Não é um repositório Git${NC}"
    echo "Execute este script no diretório do projeto SD-Resolv"
    exit 1
fi

# Verificar se há commits
if ! git log --oneline -1 > /dev/null 2>&1; then
    echo -e "${RED}❌ Erro: Nenhum commit encontrado${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Repositório Git válido encontrado${NC}"

# Mostrar status atual
echo -e "${YELLOW}📊 Status atual:${NC}"
git log --oneline -3
echo ""

# Remover remote existente (se houver)
if git remote get-url origin > /dev/null 2>&1; then
    echo -e "${YELLOW}🔄 Removendo remote origin existente...${NC}"
    git remote remove origin
fi

# Adicionar novo remote com token
echo -e "${YELLOW}🔗 Adicionando remote origin...${NC}"
git remote add origin "$REPO_URL"

# Verificar remote
echo -e "${YELLOW}🔍 Verificando remote:${NC}"
git remote -v

# Fazer push
echo -e "${YELLOW}📤 Fazendo push para o GitHub...${NC}"
echo "Repositório: https://github.com/Homura00/sdresolve"
echo ""

if git push -u origin main; then
    echo ""
    echo -e "${GREEN}🎉 SUCCESS! Código enviado para o GitHub!${NC}"
    echo ""
    echo "🌐 Seu repositório: https://github.com/Homura00/sdresolve"
    echo "📁 Arquivos enviados:"
    echo "  ✅ index.html (Website principal)"
    echo "  ✅ styles.css (Estilos)"
    echo "  ✅ fotoSdResolve.jpeg (Logo)"
    echo "  ✅ Dockerfile (Container)"
    echo "  ✅ docker-compose.yml (Orquestração)"
    echo "  ✅ deploy.sh (Script de deploy)"
    echo "  ✅ README.md (Documentação)"
    echo "  ✅ E mais 5 arquivos..."
    echo ""
    echo "🚀 Próximos passos:"
    echo "  1. Acesse: https://github.com/Homura00/sdresolve"
    echo "  2. Verifique se todos os arquivos estão lá"
    echo "  3. Configure GitHub Pages (opcional)"
    echo "  4. Adicione descrição e topics ao repositório"
    echo ""
else
    echo ""
    echo -e "${RED}❌ Erro no push!${NC}"
    echo ""
    echo "Possíveis soluções:"
    echo "  1. Verifique se o token está correto"
    echo "  2. Verifique se o token tem permissão 'repo'"
    echo "  3. Verifique se o repositório existe: https://github.com/Homura00/sdresolve"
    echo ""
    exit 1
fi
