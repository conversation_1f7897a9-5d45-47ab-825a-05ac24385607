version: '3.8'

services:
  sd-resolv-website:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sd-resolv-web
    ports:
      - "8080:80"
    restart: unless-stopped
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80
    volumes:
      # Volume para logs (opcional)
      - ./logs:/var/log/nginx
    networks:
      - sd-resolv-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.sd-resolv.rule=Host(`sd-resolv.local`)"
      - "traefik.http.services.sd-resolv.loadbalancer.server.port=80"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  sd-resolv-network:
    driver: bridge

# Configuração para produção com HTTPS (opcional)
# Descomente as linhas abaixo se usar com Traefik ou similar
#
# services:
#   sd-resolv-website:
#     labels:
#       - "traefik.enable=true"
#       - "traefik.http.routers.sd-resolv.rule=Host(`www.sd-resolv.com.br`)"
#       - "traefik.http.routers.sd-resolv.entrypoints=websecure"
#       - "traefik.http.routers.sd-resolv.tls.certresolver=letsencrypt"
