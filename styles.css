/* Custom Styles for SD-Resolv */

/* Smooth scrolling for all elements */
html {
    scroll-behavior: smooth;
}

/* Custom animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Animation classes */
.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

.pulse-hover:hover {
    animation: pulse 0.6s ease-in-out;
}

/* Enhanced gradient backgrounds */
.hero-gradient {
    background: linear-gradient(135deg, #313131 0%, #404040 50%, #313131 100%);
    position: relative;
    overflow: hidden;
}

.hero-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

/* Hero logo styles */
.hero-logo-container {
    position: relative;
    display: inline-block;
}

.hero-logo {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    animation: float 3s ease-in-out infinite;
}

.hero-logo:hover {
    transform: scale(1.05);
    box-shadow: 0 15px 40px rgba(0,0,0,0.4);
}

.hero-logo-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: all 0.3s ease;
}

.hero-logo-container:hover .hero-logo-overlay {
    opacity: 1;
}

.hero-logo-container:hover .hero-logo {
    filter: brightness(0.7);
}

/* Float animation */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Responsive hero logo */
@media (max-width: 768px) {
    .hero-logo {
        width: 150px;
        height: 150px;
    }
}

/* About section logo styles */
.about-logo-container {
    position: relative;
    display: inline-block;
}

.about-logo {
    width: 180px;
    height: 180px;
    border-radius: 20px;
    object-fit: cover;
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    border: 3px solid #fff;
}

.about-logo:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 45px rgba(0,0,0,0.2);
}

.about-logo-badge {
    position: absolute;
    bottom: -10px;
    right: -10px;
    background: white;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    font-size: 1.2rem;
}

/* Responsive about logo */
@media (max-width: 768px) {
    .about-logo {
        width: 140px;
        height: 140px;
    }

    .about-logo-badge {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}

/* Enhanced card styles */
.card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0,0,0,0.05);
}

.card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    border-color: rgba(185, 157, 87, 0.2);
}

/* Card Content Hierarchy */
.card-body {
    padding: 2rem !important;
}

.card-title {
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    margin-bottom: 1.25rem !important;
    margin-top: 0 !important;
    color: #313131 !important;
}

.card-subtitle {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 1rem;
    color: #B99D57;
}

.card-text {
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

/* Section spacing improvements */
.section-header {
    margin-bottom: 3rem;
}

.section-content {
    margin-bottom: 2rem;
}

/* List styling within cards */
.card-body ul {
    margin-bottom: 1.5rem;
}

.card-body ul li {
    margin-bottom: 0.5rem;
    padding-left: 0.25rem;
}

/* Icon spacing */
.card-body .fas, .card-body .fab {
    margin-right: 0.5rem;
}

/* Process steps spacing */
.process-step {
    margin-bottom: 2rem;
}

.process-step:last-child {
    margin-bottom: 0;
}

/* Testimonial card improvements */
.testimonial-card .card-body {
    padding: 2rem;
}

/* Contact section spacing */
.contact-section {
    padding: 4rem 0;
}

.contact-item {
    margin-bottom: 2rem;
}

/* Typography Hierarchy System */

/* H1 - Main Page Title (Hero) */
h1, .h1 {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: #EFEDE3;
}

/* H2 - Section Titles */
h2, .h2, .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 2rem;
    margin-top: 0;
    color: #313131;
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #B99D57 0%, #D4B86A 100%);
    border-radius: 2px;
    box-shadow: 0 2px 4px rgba(185, 157, 87, 0.3);
}

/* H3 - Subsection Titles */
h3, .h3 {
    font-size: 1.75rem;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 1.25rem;
    margin-top: 0;
    color: #313131;
}

/* H4 - Card Titles */
h4, .h4, .card-title {
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 1rem;
    margin-top: 0;
    color: #313131;
}

/* H5 - Process Steps and Smaller Titles */
h5, .h5 {
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 0.75rem;
    margin-top: 0;
    color: #313131;
}

/* H6 - Small Headings */
h6, .h6 {
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    margin-top: 0;
    color: #313131;
}

/* Enhanced navbar */
.navbar {
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.95) !important;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 800;
    font-size: 1.75rem;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
}

/* Logo styles */
.logo-img {
    height: 45px;
    width: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    object-fit: contain;
}

.logo-img:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    transform: scale(1.05);
}

/* Responsive logo */
@media (max-width: 768px) {
    .logo-img {
        height: 35px;
    }

    .navbar-brand {
        font-size: 1.4rem;
    }
}

/* Enhanced buttons */
.btn {
    font-weight: 600;
    border-radius: 50px;
    padding: 12px 30px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, #B99D57 0%, #D4B86A 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(185, 157, 87, 0.3);
    color: #313131 !important;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(185, 157, 87, 0.4);
    color: #313131 !important;
}

.btn.clicked {
    transform: scale(0.95);
    box-shadow: 0 2px 8px rgba(185, 157, 87, 0.6);
}

/* WhatsApp button styling */
.btn-outline-light:hover {
    background-color: #25D366 !important;
    border-color: #25D366 !important;
    transform: translateY(-2px);
    color: #EFEDE3 !important;
}

.btn-outline-light {
    border-color: #EFEDE3;
    color: #EFEDE3 !important;
}

/* Enhanced icons */
.fa-2x {
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

/* Process steps enhancement */
.process-step {
    position: relative;
    padding: 2rem;
    border-radius: 15px;
    background: white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.process-step:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

/* Testimonial cards enhancement */
.testimonial-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

/* Star ratings */
.star-rating {
    color: #ffc107;
    filter: drop-shadow(0 1px 2px rgba(255, 193, 7, 0.3));
}

/* Contact section enhancement */
.contact-item {
    padding: 1.5rem;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
}

.contact-icon {
    transition: all 0.3s ease;
    cursor: pointer;
}

.contact-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

/* WhatsApp contact link */
a[href*="wa.me"]:hover .contact-icon {
    background-color: #25D366 !important;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
    }
}

/* Footer enhancement */
footer {
    background: linear-gradient(135deg, #313131 0%, #404040 100%);
}

.footer-logo {
    height: 35px;
    width: auto;
    border-radius: 6px;
    object-fit: contain;
    transition: all 0.3s ease;
}

.footer-logo:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(255,255,255,0.2);
}

.footer-social-link {
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 50%;
}

.footer-social-link:hover {
    background: rgba(239, 237, 227, 0.1);
    transform: translateY(-2px);
    color: #B99D57 !important;
}

/* Responsive Typography */
@media (max-width: 768px) {
    .hero-gradient {
        padding: 3rem 0;
    }

    /* Mobile Typography Adjustments */
    h1, .h1 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    h2, .h2, .section-title {
        font-size: 2rem;
        margin-bottom: 1.5rem;
    }

    h3, .h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    h4, .h4, .card-title {
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }

    h5, .h5 {
        font-size: 1.125rem;
        margin-bottom: 0.5rem;
    }

    .section-header {
        margin-bottom: 2rem;
    }

    .card-body {
        padding: 1.5rem !important;
    }

    .card-hover:hover {
        transform: translateY(-3px) scale(1.01);
    }

    .lead {
        font-size: 1.1rem;
    }
}

@media (max-width: 576px) {
    h1, .h1 {
        font-size: 2rem;
    }

    h2, .h2, .section-title {
        font-size: 1.75rem;
    }

    .card-body {
        padding: 1.25rem !important;
    }

    .section-header {
        margin-bottom: 1.5rem;
    }
}

/* Loading animation for page elements */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* Utility classes */
.text-gradient {
    background: linear-gradient(135deg, #B99D57 0%, #D4B86A 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #667eea 0%, #764ba2 100%) border-box;
}
