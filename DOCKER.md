# 🐳 Docker - SD-Resolv Website

Documentação completa para containerização e deploy do website da SD-Resolv usando Docker.

## 📋 Pré-requisitos

- Docker instalado (versão 20.10+)
- Docker Compose (versão 2.0+)
- 2GB de espaço livre em disco
- Porta 8080 disponível

## 🚀 Deploy Rápido

### Opção 1: Script Automatizado (Recomendado)
```bash
# Tornar o script executável
chmod +x deploy.sh

# Deploy completo
./deploy.sh deploy

# Ou usando Docker Compose
./deploy.sh compose
```

### Opção 2: Comandos Manuais
```bash
# Build da imagem
docker build -t sd-resolv-website .

# Executar container
docker run -d --name sd-resolv-web -p 8080:80 sd-resolv-website
```

### Opção 3: Docker Compose
```bash
# Deploy com Docker Compose
docker-compose up -d --build

# Parar serviços
docker-compose down
```

## 🔧 Comandos Disponíveis

### Script de Deploy
```bash
./deploy.sh build     # Apenas constrói a imagem
./deploy.sh deploy    # Deploy completo (padrão)
./deploy.sh compose   # Deploy com Docker Compose
./deploy.sh logs      # Visualizar logs
./deploy.sh stop      # Parar container
./deploy.sh restart   # Reiniciar container
./deploy.sh clean     # Limpar containers
```

### Docker Manual
```bash
# Build
docker build -t sd-resolv-website .

# Run
docker run -d --name sd-resolv-web -p 8080:80 sd-resolv-website

# Logs
docker logs sd-resolv-web

# Stop
docker stop sd-resolv-web

# Remove
docker rm sd-resolv-web

# Health check
docker exec sd-resolv-web curl -f http://localhost/
```

## 🌐 Acesso

Após o deploy, o website estará disponível em:
- **Local**: http://localhost:8080
- **Rede**: http://[IP-DO-SERVIDOR]:8080

## 📊 Monitoramento

### Health Check
O container possui health check automático que verifica a cada 30 segundos se o website está respondendo.

```bash
# Verificar status do health check
docker inspect --format='{{.State.Health.Status}}' sd-resolv-web
```

### Logs
```bash
# Logs em tempo real
docker logs -f sd-resolv-web

# Últimas 50 linhas
docker logs --tail 50 sd-resolv-web
```

### Métricas
```bash
# Uso de recursos
docker stats sd-resolv-web

# Informações do container
docker inspect sd-resolv-web
```

## ⚙️ Configurações

### Variáveis de Ambiente
```yaml
environment:
  - NGINX_HOST=localhost
  - NGINX_PORT=80
```

### Volumes
```yaml
volumes:
  - ./logs:/var/log/nginx  # Logs do Nginx
```

### Portas
- **Container**: 80 (Nginx)
- **Host**: 8080 (configurável)

## 🔒 Segurança

O container inclui configurações de segurança:
- Headers de segurança HTTP
- Compressão gzip habilitada
- Cache otimizado para assets estáticos
- Logs de acesso e erro

### Headers de Segurança
- `X-Frame-Options: SAMEORIGIN`
- `X-Content-Type-Options: nosniff`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: no-referrer-when-downgrade`

## 🚀 Deploy em Produção

### 1. Servidor VPS/Cloud
```bash
# Clone do repositório
git clone [REPO-URL]
cd sd-resolv

# Deploy
./deploy.sh compose

# Configurar proxy reverso (Nginx/Apache)
# Configurar SSL/TLS (Let's Encrypt)
```

### 2. Docker Swarm
```bash
# Inicializar swarm
docker swarm init

# Deploy do stack
docker stack deploy -c docker-compose.yml sd-resolv
```

### 3. Kubernetes
```yaml
# Criar deployment e service
kubectl apply -f k8s/
```

## 🔧 Troubleshooting

### Container não inicia
```bash
# Verificar logs
docker logs sd-resolv-web

# Verificar se a porta está em uso
netstat -tulpn | grep 8080

# Verificar imagem
docker images | grep sd-resolv
```

### Website não carrega
```bash
# Testar conectividade
curl -I http://localhost:8080

# Verificar configuração do Nginx
docker exec sd-resolv-web nginx -t

# Reiniciar Nginx
docker exec sd-resolv-web nginx -s reload
```

### Performance
```bash
# Verificar recursos
docker stats sd-resolv-web

# Otimizar imagem
docker system prune -a
```

## 📁 Estrutura de Arquivos

```
SD-Resolv/
├── Dockerfile              # Configuração do container
├── docker-compose.yml      # Orquestração de serviços
├── .dockerignore           # Arquivos ignorados no build
├── deploy.sh               # Script de deploy automatizado
├── DOCKER.md               # Esta documentação
├── index.html              # Página principal
├── styles.css              # Estilos CSS
├── fotoSdResolve.jpeg      # Logo da empresa
└── README.md               # Documentação geral
```

## 🔄 Atualizações

### Atualizar Website
```bash
# Parar container atual
./deploy.sh stop

# Rebuild e redeploy
./deploy.sh deploy

# Ou com zero downtime
./deploy.sh compose
```

### Backup
```bash
# Backup da imagem
docker save sd-resolv-website > sd-resolv-backup.tar

# Restaurar backup
docker load < sd-resolv-backup.tar
```

## 📞 Suporte

Para suporte técnico:
- **Email**: <EMAIL>
- **WhatsApp**: https://wa.me/6286069094

---

**Desenvolvido com ❤️ para SD-Resolv**
